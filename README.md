# 🚀 币安量化交易机器人 - 智能版 v2.0

一个基于PyQt6的现代化币安量化交易机器人，集成AI分析、技术指标、新闻情感分析等功能。

## ✨ 主要功能

### 📊 技术分析
- **多种技术指标**: RSI、MACD、布林带、ADX、EMA、随机指标等
- **实时K线图表**: 支持多种时间周期（1m、5m、15m、1h、4h、1d）
- **智能趋势分析**: 基于ADX的趋势强度判断
- **自适应权重系统**: 根据市场状态动态调整指标权重

### 🤖 AI驱动交易
- **DeepSeek AI集成**: 智能市场分析和交易建议
- **新闻情感分析**: 实时新闻对市场影响的评估
- **多重确认机制**: 降低误判风险
- **风险管理**: 自动止盈止损设置

### 💼 交易管理
- **实时账户监控**: 余额、持仓、订单状态
- **自动交易执行**: 支持市价单和限价单
- **风险控制**: 可配置的止盈止损比例
- **交易日志**: 详细的交易记录和分析

### 🎨 现代化界面
- **响应式设计**: 适配不同屏幕尺寸
- **动态主题**: 实时背景效果
- **直观操作**: 用户友好的界面设计
- **实时更新**: 数据自动刷新

## 🛠️ 安装说明

### 环境要求
- Python 3.8+
- PyQt6
- 网络连接

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd BIAN
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

3. **配置API密钥**
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑.env文件，填入您的API密钥
nano .env
```

4. **运行程序**
```bash
python main_window.py
```

## 🔧 配置说明

### API密钥配置
在`.env`文件中配置以下API密钥：

- `OKX_API_KEY`: OKX API密钥（必需）
- `OKX_SECRET_KEY`: OKX Secret密钥（必需）
- `OKX_PASSPHRASE`: OKX Passphrase（必需）
- `NEWS_API_KEY`: 新闻API密钥（可选）
- `DEEPSEEK_API_KEY`: DeepSeek AI API密钥（可选）

### 交易参数配置
- 止盈比例：默认1.0%，可在界面中调整
- 止损比例：默认3.0%，可在界面中调整
- 更新频率：5秒，可根据需要调整

## 📈 使用指南

### 基本操作
1. **启动程序**: 运行`python main_window.py`
2. **选择交易对**: 在顶部下拉菜单中选择
3. **设置参数**: 配置止盈止损比例
4. **开启自动交易**: 点击"启动自动交易"按钮

### 高级功能
- **指标设置**: 点击"指标设置"按钮自定义技术指标参数
- **AI分析**: 查看AI分析结果和交易建议
- **风险管理**: 根据市场情况调整交易参数

## ⚠️ 风险提示

- **投资有风险**: 量化交易存在亏损风险，请谨慎投资
- **测试建议**: 建议先在测试环境中运行
- **资金管理**: 不要投入超过承受能力的资金
- **监控重要**: 定期检查交易状态和账户余额

## 🔒 安全说明

- **API权限**: 建议只开启必要的交易权限
- **密钥保护**: 妥善保管API密钥，定期更换
- **网络安全**: 确保在安全的网络环境中运行
- **版本控制**: 不要将包含真实密钥的文件提交到代码仓库

## 📞 技术支持

如有问题或建议，请通过以下方式联系：
- 创建Issue
- 发送邮件
- 技术交流群

## 📄 许可证

本项目仅供学习和研究使用，请遵守相关法律法规。

---

**免责声明**: 本软件仅供教育和研究目的，使用者需自行承担交易风险。
